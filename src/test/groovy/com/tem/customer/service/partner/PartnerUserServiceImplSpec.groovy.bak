package com.tem.customer.service.partner

import com.iplatform.common.OrderBizType
import com.iplatform.common.ResponseDto
import com.iplatform.common.utils.DateUtils
import com.tem.customer.model.vo.common.UserOrderInfoVO
import com.tem.customer.shared.exception.BusinessException
import com.tem.oms.api.OrderService
import com.tem.oms.dto.OrderDto
import com.tem.oms.enums.ShowStatusEnum
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.UserDto
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate

/**
 * 企业用户服务实现类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
class PartnerUserServiceImplSpec extends Specification {

    @Subject
    PartnerUserServiceImpl service

    UserService userService = Mock()
    OrderService orderService = Mock()

    def setup() {
        service = new PartnerUserServiceImpl()
        service.userService = userService
        service.orderService = orderService
    }

    def "测试查询用户订单信息 - 成功"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, partnerId, "测试用户")
        def orderDtos = [
                createOrderDto(1L, userId, OrderBizType.FLIGHT, "2025-07-30", 10000),
                createOrderDto(2L, userId, OrderBizType.HOTEL, "2025-07-29", 20000)
        ]
        
        and: "Mock依赖方法"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success(orderDtos)

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "验证结果"
        result.size() == 2
        result[0].id == 1L
        result[0].totalAmount == new BigDecimal("100.00")
        result[1].id == 2L
        result[1].totalAmount == new BigDecimal("200.00")
    }

    def "测试查询用户订单信息 - 参数为空"() {
        when: "传入空参数"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("不能为空")

        where:
        partnerId | userId
        null      | 2001L
        1001L     | null
        null      | null
    }

    def "测试查询用户订单信息 - 用户不存在"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        and: "Mock用户不存在"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(null)

        when: "调用查询方法"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("用户信息不存在")
    }

    def "测试查询用户订单信息 - 用户不属于指定企业"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, 1002L, "测试用户")
        
        and: "Mock用户属于其他企业"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)

        when: "调用查询方法"
        service.getUserOrders(partnerId, userId)

        then: "抛出业务异常"
        def ex = thrown(BusinessException)
        ex.message.contains("用户不属于指定企业")
    }

    def "测试查询用户订单信息 - 无订单数据"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        
        def userDto = createUserDto(userId, partnerId, "测试用户")
        
        and: "Mock无订单数据"
        userService.getUserBaseInfo(userId) >> ResponseDto.success(userDto)
        orderService.queryOrderListByCs(partnerId, userId, _) >> ResponseDto.success([])

        when: "调用查询方法"
        def result = service.getUserOrders(partnerId, userId)

        then: "返回空列表"
        result.isEmpty()
    }

    def "测试处理订单数据 - 过滤已取消订单"() {
        given: "准备测试数据"
        def orderDtos = [
                createOrderDto(1L, 2001L, OrderBizType.FLIGHT, "2025-07-30", 10000, "CANCELED"),
                createOrderDto(2L, 2001L, OrderBizType.HOTEL, "2025-07-29", 20000, "CONFIRMED")
        ]

        when: "调用处理方法"
        def result = service.processOrderData(orderDtos)

        then: "验证结果"
        result.size() == 1
        result[0].id == 2L
    }

    def "测试处理订单数据 - 过滤无行程时间订单"() {
        given: "准备测试数据"
        def orderDtos = [
                createOrderDto(1L, 2001L, OrderBizType.FLIGHT, null, 10000),
                createOrderDto(2L, 2001L, OrderBizType.HOTEL, "2025-07-29", 20000)
        ]

        when: "调用处理方法"
        def result = service.processOrderData(orderDtos)

        then: "验证结果"
        result.size() == 1
        result[0].id == 2L
    }

    def "测试处理订单数据 - 排序和数量限制"() {
        given: "准备测试数据"
        def orderDtos = []
        for (i in 1..25) {
            orderDtos.add(createOrderDto(i, 2001L, OrderBizType.FLIGHT, "2025-07-${30-i}", 10000))
        }

        when: "调用处理方法"
        def result = service.processOrderData(orderDtos)

        then: "验证结果"
        result.size() == 20
        result[0].id == 1L
        result[19].id == 20L
    }

    def "测试转换订单DTO为VO - 机票订单"() {
        given: "准备测试数据"
        def orderDto = createFlightOrderDto(1L, 2001L, "2025-07-30", 10000)

        when: "调用转换方法"
        def result = service.convertToOrderVO(orderDto)

        then: "验证结果"
        result
        result.id == 1L
        result.bizType == "1"
        result.totalAmount == new BigDecimal("100.00")
        result.trip == "上海-北京"
        result.orderShowStatus == "已出票"
    }

    def "测试转换订单DTO为VO - 酒店订单"() {
        given: "准备测试数据"
        def orderDto = createHotelOrderDto(1L, 2001L, "2025-07-30", "2025-07-31", 20000)

        when: "调用转换方法"
        def result = service.convertToOrderVO(orderDto)

        then: "验证结果"
        result
        result.id == 1L
        result.bizType == "4"
        result.totalAmount == new BigDecimal("200.00")
        result.hotelName == "测试酒店"
        result.stayDays == 1
        result.orderShowStatus == "已确认"
    }

    def "测试转换订单DTO为VO - 火车票订单"() {
        given: "准备测试数据"
        def orderDto = createTrainOrderDto(1L, 2001L, "2025-07-30", 5000)

        when: "调用转换方法"
        def result = service.convertToOrderVO(orderDto)

        then: "验证结果"
        result
        result.id == 1L
        result.bizType == "2"
        result.totalAmount == new BigDecimal("50.00")
        result.trip == "上海站-北京站"
        result.orderShowStatus == "已出票"
    }

    def "测试转换订单DTO为VO - 转换异常"() {
        given: "准备测试数据"
        def orderDto = new OrderDto()
        orderDto.id = 1L
        orderDto.bizType = 999 // 无效的业务类型

        when: "调用转换方法"
        def result = service.convertToOrderVO(orderDto)

        then: "返回null"
        result == null
    }

    def "测试判断订单是否有效 - 有效订单"() {
        given: "准备测试数据"
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.FLIGHT, "2025-07-30", 10000, "CONFIRMED")

        when: "调用判断方法"
        def result = service.isValidOrder(orderDto)

        then: "验证结果"
        result
    }

    def "测试判断订单是否有效 - 已取消订单"() {
        given: "准备测试数据"
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.FLIGHT, "2025-07-30", 10000, "CANCELED")

        when: "调用判断方法"
        def result = service.isValidOrder(orderDto)

        then: "验证结果"
        !result
    }

    def "测试判断订单是否有效 - 无行程时间"() {
        given: "准备测试数据"
        def orderDto = createOrderDto(1L, 2001L, OrderBizType.FLIGHT, null, 10000, "CONFIRMED")

        when: "调用判断方法"
        def result = service.isValidOrder(orderDto)

        then: "验证结果"
        !result
    }

    def "测试根据业务类型代码获取OrderBizType枚举"() {
        expect: "验证结果"
        PartnerUserServiceImpl.getOrderBizTypeByCode(bizTypeCode) == expectedOrderBizType

        where:
        bizTypeCode | expectedOrderBizType
        1           | OrderBizType.FLIGHT
        2           | OrderBizType.TRAIN
        4           | OrderBizType.HOTEL
        5           | OrderBizType.INSURANCE
        6           | OrderBizType.GENERAL
        7           | OrderBizType.INTL_FLIGHT
        null        | null
        999         | null
    }

    /**
     * 创建测试用的UserDto对象
     */
    private static UserDto createUserDto(Long id, Long partnerId, String name) {
        UserDto userDto = new UserDto()
        userDto.id = id
        userDto.partnerId = partnerId
        userDto.fullname = name
        userDto.mobile = "13800138000"
        userDto.email = "<EMAIL>"
        return userDto
    }

    /**
     * 创建测试用的OrderDto对象
     */
    private static OrderDto createOrderDto(Long id, Long userId, OrderBizType bizType, String travelStartTime, Integer totalAmount, String orderShowStatus = "CONFIRMED") {
        OrderDto orderDto = new OrderDto()
        orderDto.id = id
        orderDto.userId = userId
        orderDto.bizType = bizType.getCode()
        orderDto.travelStartTime = travelStartTime ? DateUtils.parseDate(travelStartTime) : null
        orderDto.totalAmount = totalAmount
        orderDto.orderShowStatus = orderShowStatus
        orderDto.orderTravellerNames = "测试乘客"
        return orderDto
    }

    /**
     * 创建测试用的机票订单Dto对象
     */
    private static OrderDto createFlightOrderDto(Long id, Long userId, String travelStartTime, Integer totalAmount) {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.FLIGHT, travelStartTime, totalAmount)
        
        def orderDetail = new OrderDto.OrderDetail()
        def flightDetail = new OrderDto.FlightOrderDetailDto()
        flightDetail.fromCityName = "上海"
        flightDetail.toCityName = "北京"
        orderDetail.flightOrderDetailDtos = [flightDetail]
        orderDto.orderDetail = orderDetail
        
        return orderDto
    }

    /**
     * 创建测试用的酒店订单Dto对象
     */
    private static OrderDto createHotelOrderDto(Long id, Long userId, String checkInDate, String checkOutDate, Integer totalAmount) {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.HOTEL, checkInDate, totalAmount)
        
        def orderDetail = new OrderDto.OrderDetail()
        def hotelDetail = new OrderDto.HotelOrderDetailDto()
        hotelDetail.hotelName = "测试酒店"
        hotelDetail.hotelAddress = "测试地址"
        hotelDetail.checkInDate = DateUtils.parseDate(checkInDate)
        hotelDetail.checkOutDate = DateUtils.parseDate(checkOutDate)
        orderDetail.hotelOrderDetailDto = hotelDetail
        orderDto.orderDetail = orderDetail
        
        return orderDto
    }

    /**
     * 创建测试用的火车票订单Dto对象
     */
    private static OrderDto createTrainOrderDto(Long id, Long userId, String travelStartTime, Integer totalAmount) {
        OrderDto orderDto = createOrderDto(id, userId, OrderBizType.TRAIN, travelStartTime, totalAmount)
        
        def orderDetail = new OrderDto.OrderDetail()
        def trainDetail = new OrderDto.TrainOrderDetailDto()
        trainDetail.fromStation = "上海站"
        trainDetail.arriveStation = "北京站"
        orderDetail.trainOrderDetailDtos = [trainDetail]
        orderDto.orderDetail = orderDetail
        
        return orderDto
    }
}